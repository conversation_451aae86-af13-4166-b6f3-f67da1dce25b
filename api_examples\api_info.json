{"endpoints": {"获取所有扫描任务": {"description": "获取绿盟系统中的所有扫描任务列表", "method": "GET", "url": "/api/tasks"}, "获取指定任务的子任务": {"description": "获取指定任务的所有子任务信息", "method": "GET", "parameters": {"task_id": "任务ID (路径参数)"}, "url": "/api/tasks/<task_id>/subtasks"}, "获取指定任务的所有漏洞信息": {"description": "获取指定任务的所有漏洞详细信息", "method": "GET", "parameters": {"task_id": "任务ID (路径参数)"}, "url": "/api/tasks/<task_id>/vulnerabilities"}}, "message": "绿盟漏洞扫描器 Flask API 服务", "version": "1.0"}