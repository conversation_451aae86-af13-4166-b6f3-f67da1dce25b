# API响应示例文件说明

本目录包含了绿盟漏洞扫描器Flask API的实际响应示例文件，这些文件是通过以下curl命令从真实API服务中获取的：

## 文件列表与对应接口

### 1. api_info.json
**对应接口**: `GET /`  
**获取命令**: 
```bash
curl http://0.0.0.0:35000/ > api_info.json
```
**文件说明**: 
- 包含API服务的基本信息
- 列出所有可用的端点和描述
- 显示API版本信息

### 2. tasks_list.json
**对应接口**: `GET /api/tasks`  
**获取命令**: 
```bash
curl http://0.0.0.0:35000/api/tasks > tasks_list.json
```
**文件说明**: 
- 包含系统中所有扫描任务的完整列表
- 展示了不同类型任务的字段结构
- 包含定时任务和立即执行任务的示例
- 文件大小约221KB，包含大量真实任务数据

### 3. task_subtasks.json
**对应接口**: `GET /api/tasks/555/subtasks`  
**获取命令**: 
```bash
curl http://0.0.0.0:35000/api/tasks/555/subtasks > task_subtasks.json
```
**文件说明**: 
- 展示任务ID为555的所有子任务
- 包含分页信息结构
- 显示定时任务的执行历史
- 包含11个子任务的完整信息

### 4. task_vulnerabilities.json
**对应接口**: `GET /api/tasks/555/vulnerabilities`  
**获取命令**: 
```bash
curl http://0.0.0.0:35000/api/tasks/555/vulnerabilities > task_vulnerabilities.json
```
**文件说明**: 
- 包含任务555发现的所有漏洞详细信息
- 文件大小约2.6MB，包含大量漏洞数据
- 展示了完整的漏洞字段结构
- 包含CVE、CNVD、CNNVD等多种漏洞编号
- 包含漏洞描述、解决方案等详细信息

## 数据特点

### 真实性
- 所有数据都来自真实的绿盟漏洞扫描器系统
- 反映了实际生产环境中的数据结构和内容
- 包含了各种类型的任务和漏洞信息

### 完整性
- 包含了API所有主要端点的响应示例
- 展示了完整的字段结构和数据类型
- 涵盖了不同状态和类型的任务

### 实用性
- 可以直接用于API开发和测试
- 帮助理解字段含义和数据格式
- 提供了真实的数据量级参考

## 使用建议

1. **开发参考**: 使用这些文件了解API响应的实际结构
2. **测试数据**: 可以作为单元测试和集成测试的参考数据
3. **字段理解**: 通过真实数据理解各字段的实际含义和格式
4. **性能评估**: 了解实际数据量，为性能优化提供参考

## 注意事项

1. **数据安全**: 这些文件包含真实的系统数据，请注意保护数据安全
2. **时效性**: 数据获取时间为2025年7月25日，实际系统数据可能已发生变化
3. **敏感信息**: 文件中可能包含IP地址等敏感信息，请谨慎处理
4. **文件大小**: 部分文件较大，加载时请注意性能影响

## 相关文档

- [API接口文档.md](../API接口文档.md) - 完整的API接口文档
- 各JSON文件 - 对应的实际响应示例

## 更新记录

- 2025-07-25: 初始创建，包含4个主要接口的响应示例
