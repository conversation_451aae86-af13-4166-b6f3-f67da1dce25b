# 绿盟漏洞扫描器 Flask API 接口文档

## 概述
绿盟漏洞扫描器 Flask API 服务，版本 1.0，提供漏洞扫描任务管理和漏洞信息查询功能。

## 基础信息
- **服务名称**: 绿盟漏洞扫描器 Flask API 服务
- **版本**: 1.0
- **主机**: ************

## 接口列表

### 1. 获取API服务信息
**接口描述**: 获取API服务的基本信息和可用端点列表

**请求方式**: `GET`

**请求URL**: `/`

**响应示例**:
```json
{
  "endpoints": {
    "获取所有扫描任务": {
      "description": "获取绿盟系统中的所有扫描任务列表", 
      "method": "GET", 
      "url": "/api/tasks"
    }, 
    "获取指定任务的子任务": {
      "description": "获取指定任务的所有子任务信息", 
      "method": "GET", 
      "parameters": {
        "task_id": "任务ID (路径参数)"
      }, 
      "url": "/api/tasks/<task_id>/subtasks"
    }, 
    "获取指定任务的所有漏洞信息": {
      "description": "获取指定任务的所有漏洞详细信息", 
      "method": "GET", 
      "parameters": {
        "task_id": "任务ID (路径参数)"
      }, 
      "url": "/api/tasks/<task_id>/vulnerabilities"
    }
  }, 
  "message": "绿盟漏洞扫描器 Flask API 服务", 
  "version": "1.0"
}
```

### 2. 获取所有扫描任务
**接口描述**: 获取绿盟系统中的所有扫描任务列表

**请求方式**: `GET`

**请求URL**: `/api/tasks`

**响应字段说明**:
```json
{
  "result": {
    "host": "string",           // 主机地址
    "tasks": [                  // 任务列表
      {
        "execution_time": "string",        // 执行时间描述
        "not_scan_id": "integer",          // 未扫描ID (0表示正常)
        "risk_task_point": "string",       // 风险等级 (high/middle/low/safe/unknown)
        "scan_vuln_count": {               // 扫描漏洞统计
          "high_count": "integer",         // 高危漏洞数量
          "low_count": "integer",          // 低危漏洞数量
          "medium_count": "integer",       // 中危漏洞数量
          "total_count": "integer"         // 总漏洞数量
        },
        "task_begin_time": "string|null",  // 任务开始时间 (ISO格式)
        "task_create_time": "string",      // 任务创建时间 (ISO格式)
        "task_cron_time": "string|null",   // 定时任务配置
        "task_exec_type": "integer",       // 执行类型 (1=立即执行, 3=定时执行)
        "task_id": "integer",              // 任务ID
        "task_name": "string",             // 任务名称
        "task_parent_id": "integer|null",  // 父任务ID (-1表示无父任务)
        "task_start_scan": "string|null",  // 扫描开始时间 (ISO格式)
        "task_status": "integer",          // 任务状态 (3=等待中, 9=暂停, 15=已完成)
        "task_type": "integer",            // 任务类型 (1=IP扫描, 8=Web扫描)
        "tasks_exp_info": {                // 任务扩展信息
          "task_exp_child": "boolean"      // 是否有子任务
        },
        "time_end_scan": "string|null"     // 扫描结束时间 (ISO格式)
      }
    ]
  }
}
```

**字段详细说明**:
- `risk_task_point`: 任务风险等级
  - `high`: 高风险
  - `middle`: 中等风险  
  - `low`: 低风险
  - `safe`: 安全
  - `unknown`: 未知
- `task_exec_type`: 执行类型
  - `1`: 立即执行
  - `3`: 定时执行
- `task_status`: 任务状态
  - `3`: 等待中
  - `9`: 暂停
  - `15`: 已完成
- `task_type`: 任务类型
  - `1`: IP扫描
  - `8`: Web扫描
- `task_cron_time`: 定时任务时间格式说明
  - 格式: `类型#周期#时间#*#星期#第几周#*/月`
  - 示例: `2#week#09:00#*#1#*/1#*` 表示每周一09:00执行

### 3. 获取指定任务的子任务
**接口描述**: 获取指定任务的所有子任务信息

**请求方式**: `GET`

**请求URL**: `/api/tasks/{task_id}/subtasks`

**路径参数**:
- `task_id` (integer): 任务ID

**响应字段说明**:
```json
{
  "result": {
    "host": "string",                    // 主机地址
    "paginator_data": {                  // 分页信息
      "current_page": "integer",         // 当前页码
      "items_per_page": "integer",       // 每页条数
      "total_child_tasks": "integer",    // 子任务总数
      "total_pages": "integer"           // 总页数
    },
    "subtasks": [                        // 子任务列表
      {
        "execution_time": "string",      // 执行时间
        "risk_task_point": "string",     // 风险等级
        "scan_vuln_count": {             // 漏洞统计 (同上)
          "high_count": "integer",
          "low_count": "integer", 
          "medium_count": "integer",
          "total_count": "integer"
        },
        "task_begin_time": "string",     // 任务开始时间
        "task_create_time": "string",    // 任务创建时间
        "task_cron_time": "string",      // 定时配置
        "task_exec_type": "integer",     // 执行类型
        "task_id": "integer",            // 子任务ID
        "task_name": "string",           // 子任务名称
        "task_parent_id": "integer",     // 父任务ID
        "task_start_scan": "string",     // 扫描开始时间
        "task_status": "integer",        // 任务状态
        "task_type": "integer",          // 任务类型
        "tasks_exp_info": {},            // 扩展信息
        "time_end_scan": "string"        // 扫描结束时间
      }
    ],
    "task_id": "integer",                // 父任务ID
    "total_subtasks": "integer"          // 子任务总数
  },
  "status": "integer"                    // 响应状态码 (0=成功)
}
```

### 4. 获取指定任务的所有漏洞信息
**接口描述**: 获取指定任务的所有漏洞详细信息

**请求方式**: `GET`

**请求URL**: `/api/tasks/{task_id}/vulnerabilities`

**路径参数**:
- `task_id` (integer): 任务ID

**响应字段说明**:
```json
{
  "result": {
    "host": "string",                    // 主机地址
    "hosts_count": "integer",            // 受影响主机数量
    "task_id": "integer",                // 任务ID
    "total_vulns": "integer",            // 漏洞总数
    "vuln_level_count": {                // 漏洞等级统计
      "high": "integer",                 // 高危漏洞数量
      "low": "integer",                  // 低危漏洞数量
      "middle": "integer",               // 中危漏洞数量
      "vulns_count": "integer",          // 漏洞总数
      "vulns_exp_completed": "integer",  // 已完成利用的漏洞数
      "vulns_support_exp": "integer"     // 支持利用的漏洞数
    },
    "vulns": [                           // 漏洞详细列表
      {
        "bugtraq_id": "string",          // Bugtraq ID
        "cncve": "string",               // 中国CVE编号
        "cnnvd": "string",               // 中国国家漏洞库编号
        "cnvd": "string",                // 中国漏洞库编号
        "cve_id": "string",              // CVE编号
        "cvss": "string",                // CVSS评分
        "date_found": "string",          // 发现日期
        "date_recorded": "string",       // 记录日期
        "edb": "string",                 // Exploit-DB编号
        "exp_desc": "string|null",       // 利用描述
        "familiar": "integer",           // 熟悉度评分
        "host_count": "integer",         // 受影响主机数量
        "i18n_description": ["string"],  // 漏洞描述 (多语言)
        "i18n_morelinks": "string",      // 更多链接
        "i18n_name": "string",           // 漏洞名称 (本地化)
        "i18n_patch": "string",          // 补丁信息
        "i18n_solution": ["string"],     // 解决方案 (多语言)
        "id": "integer",                 // 漏洞记录ID
        "is_dangerous": "boolean",       // 是否危险
        "ms_security_bulletin": "string", // 微软安全公告
        "nsfocus_id": "string",          // 绿盟漏洞ID
        "osvdb": "string",               // OSVDB编号
        "patch": "string",               // 补丁信息
        "plugin_id": "integer",          // 插件ID
        "reserved1": ["string"],         // 保留字段1 (CVSS详细信息)
        "reserved2": "string|null",      // 保留字段2
        "scan_method": "integer",        // 扫描方法
        "severity_points": "float",      // 严重性评分
        "target": "string",              // 目标主机 (分号分隔)
        "threat_level": "integer",       // 威胁等级
        "vul_confirmed": "boolean",      // 漏洞是否确认
        "vul_id": "integer",             // 漏洞ID
        "vuln_count": "integer",         // 漏洞实例数量
        "vuln_level": "string"           // 漏洞等级 (high/middle/low)
      }
    ]
  }
}
```

**漏洞等级说明**:
- `high`: 高危漏洞 - 可能导致系统完全被控制
- `middle`: 中危漏洞 - 可能导致部分信息泄露或功能受限
- `low`: 低危漏洞 - 影响较小的安全问题

**威胁等级说明**:
- `0`: 信息收集
- `1`: 低威胁
- `2`: 中等威胁
- `3`: 高威胁
- `4`: 严重威胁

**扫描方法说明**:
- `1`: 端口扫描
- `2`: 服务识别
- `3`: 漏洞检测
- `4`: 深度检测

**CVSS评分说明**:
- `reserved1[0]`: CVSS v2.0 评分和向量
- `reserved1[1]`: CVSS v3.0 评分和向量
- `reserved1[2]`: 额外评分信息

## 错误码说明
- `status: 0`: 请求成功
- `status: 1`: 请求失败
- `status: 2`: 参数错误
- `status: 3`: 权限不足
- `status: 4`: 资源不存在

## 使用示例

### 获取所有任务
```bash
curl -X GET "http://************:35000/api/tasks"
```

### 获取任务555的子任务
```bash
curl -X GET "http://************:35000/api/tasks/555/subtasks"
```

### 获取任务555的漏洞信息
```bash
curl -X GET "http://************:35000/api/tasks/555/vulnerabilities"
```

## API响应示例文件

为了帮助开发者更好地理解API接口的响应格式，我们提供了实际的API响应示例文件，存放在 `api_examples/` 目录下：

| 文件名 | 对应接口 | 说明 |
|--------|----------|------|
| `api_info.json` | `GET /` | API服务基本信息和端点列表 |
| `tasks_list.json` | `GET /api/tasks` | 所有扫描任务列表的完整响应 |
| `task_subtasks.json` | `GET /api/tasks/555/subtasks` | 任务555的子任务列表响应示例 |
| `task_vulnerabilities.json` | `GET /api/tasks/555/vulnerabilities` | 任务555的漏洞信息响应示例 |

### 示例文件使用说明

1. **api_info.json**: 展示了API服务的基本信息，包括版本号和所有可用的端点
2. **tasks_list.json**: 包含了系统中所有扫描任务的详细信息，可以看到不同类型任务的完整字段结构
3. **task_subtasks.json**: 展示了定时任务的子任务结构，包含分页信息和子任务详情
4. **task_vulnerabilities.json**: 包含了完整的漏洞信息结构，展示了各种漏洞字段的实际数据格式

这些示例文件都是从实际运行的API服务中获取的真实数据，可以作为开发和测试的参考。

## 注意事项
1. 所有时间字段均采用ISO 8601格式 (YYYY-MM-DDTHH:MM:SS)
2. 漏洞数据量可能较大，建议根据需要进行分页处理
3. 部分字段可能为null，请在使用时进行空值检查
4. CVE、CNVD等编号字段可用于关联外部漏洞数据库
5. target字段包含多个IP时使用分号(;)分隔
6. 示例文件中的数据为真实环境数据，请注意数据安全
