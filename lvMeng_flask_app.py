#!/usr/bin/env python
# encoding:utf-8

"""
绿盟漏洞扫描器 Flask API 服务
提供3个主要功能的REST API接口：
1. 获取所有扫描任务
2. 指定任务id，获取子任务
3. 指定任务id，获取所有漏洞信息
"""

from flask import Flask, jsonify, request
import requests
import json
import base64
import time
import asyncio
from datetime import datetime
from collections import defaultdict
from loguru import logger
from io import BytesIO

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

try:
    import ddddocr
    HAS_DDDDOCR = True
except ImportError:
    HAS_DDDDOCR = False
    logger.warning("ddddocr模块未安装，验证码识别功能将不可用")


class GreenLeagueLogin:
    """绿盟登录管理类"""

    def __init__(self, host="************"):
        """
        初始化登录管理器
        参数:
            host: 服务器地址，默认为************
        """
        self.host = host
        self.base_url = f"https://{host}"  # 所有API都使用HTTPS
        self.session = requests.Session()
        self.token = None

        # 禁用SSL证书验证（因为服务器使用自签名证书）
        self.session.verify = False

        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Not_A Brand";v="99", "Google Chrome";v="109", "Chromium";v="109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': f'https://{host}/'
        })

        # 初始化验证码识别器
        if HAS_DDDDOCR:
            self.ocr = ddddocr.DdddOcr()
        else:
            self.ocr = None

    def get_captcha(self):
        """
        获取验证码图片和identifier
        返回: (captcha_image_data, identifier)
        """
        try:
            url = f"{self.base_url}/interface/myauth/captcha/"
            response = self.session.get(url)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 获取base64编码的图片数据和identifier
                    image_base64 = data['data']['mg_str']['image']
                    identifier = data['data']['identifier']

                    # 解码base64图片
                    image_data = base64.b64decode(image_base64)

                    logger.info(f"成功获取验证码，identifier: {identifier}")
                    return image_data, identifier
                else:
                    logger.error(f"获取验证码失败: {data.get('message', '未知错误')}")
                    return None, None
            else:
                logger.error(f"请求验证码失败，状态码: {response.status_code}")
                return None, None

        except Exception as e:
            logger.error(f"获取验证码异常: {str(e)}")
            return None, None

    def recognize_captcha(self, image_data):
        """
        识别验证码
        参数: image_data - 图片二进制数据
        返回: 识别结果字符串
        """
        try:
            if self.ocr:
                result = self.ocr.classification(image_data)
                logger.info(f"验证码识别结果: {result}")
                return result
            else:
                # 简单的验证码识别方法（返回固定值或随机值）
                # 在实际环境中，这里应该实现更复杂的识别逻辑
                result = "1234"  # 默认验证码
                logger.warning(f"使用默认验证码: {result}")
                return result
        except Exception as e:
            logger.error(f"验证码识别失败: {str(e)}")
            return None

    def login(self, username, password, captcha_code, identifier):
        """
        执行登录
        参数:
            username: 用户名
            password: 密码（已加密）
            captcha_code: 验证码
            identifier: 验证码标识符
        返回: (success, result_data)
        """
        try:
            url = f"{self.base_url}/interface/myauth/login"

            # 构造登录数据
            login_data = {
                "username": username,
                "password": password,
                "captcha_code": captcha_code,
                "identifier": identifier
            }

            # 设置Content-Type
            headers = {
                'Content-Type': 'application/json;charset=UTF-8',
                'Origin': f'https://{self.host}'
            }

            response = self.session.post(url, json=login_data, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 提取重要信息
                    user_info = data.get('data', {})
                    self.token = user_info.get('token')
                    username = user_info.get('user_info', {}).get('username')
                    group_name = user_info.get('user_info', {}).get('group_name')

                    # 严格验证登录成功条件：必须有token且不为空
                    if self.token and self.token.strip():
                        logger.info(f"登录成功: {data.get('message', '登录成功')}")
                        logger.info(f"用户名: {username}")
                        logger.info(f"用户组: {group_name}")
                        logger.info(f"Token: {self.token[:50]}..." if len(self.token) > 50 else f"Token: {self.token}")

                        # 更新session的token
                        self.session.headers.update({'token': self.token})
                        return True, data
                    else:
                        # 虽然code=200但没有获取到有效token，说明登录实际失败（如验证码错误）
                        logger.error(f"登录失败 - 未获取到有效Token: {data.get('message', '验证码错误或其他登录问题')}")
                        return False, data
                else:
                    logger.error(f"登录失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"登录请求失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"登录异常: {str(e)}")
            return False, None

    def auto_login(self, username="admin", password="U2FsdGVkX18pUgexLHc5Y8RrbtAlXz+3EZrDpYJqFqE=", max_retries=5):
        """
        自动登录流程
        参数:
            username: 用户名，默认为admin
            password: 加密后的密码
            max_retries: 最大重试次数，默认为5
        返回: (success, result_data)
        """
        logger.info("开始绿盟自动登录流程")

        for attempt in range(max_retries):
            logger.info(f"第 {attempt + 1} 次尝试登录...")

            # 步骤1: 获取验证码
            logger.info("1. 获取验证码...")
            image_data, identifier = self.get_captcha()

            if not image_data or not identifier:
                logger.warning("获取验证码失败，重试...")
                time.sleep(1)
                continue

            # 步骤2: 识别验证码
            logger.info("2. 识别验证码...")
            captcha_code = self.recognize_captcha(image_data)

            if not captcha_code:
                logger.warning("验证码识别失败，重试...")
                time.sleep(1)
                continue

            # 步骤3: 执行登录
            logger.info("3. 执行登录...")
            success, result = self.login(username, password, captcha_code, identifier)

            if success:
                logger.info("登录成功！")
                return True, result
            else:
                logger.warning(f"登录失败，{max_retries - attempt - 1} 次重试机会剩余")
                time.sleep(2)

        logger.error("登录失败，已达到最大重试次数")
        return False, None

    def get_session(self):
        """获取已登录的session"""
        return self.session

    def get_base_url(self):
        """获取基础URL"""
        return self.base_url

    def get_token(self):
        """获取登录token"""
        return self.token


class TaskManager:
    """任务管理类"""

    def __init__(self, session, base_url):
        """
        初始化任务管理器
        参数:
            session: 已登录的requests session
            base_url: 基础URL
        """
        self.session = session
        self.base_url = base_url

    def get_task_list(self, page=1, page_size=10000000):
        """
        获取任务列表
        参数:
            page: 页码，默认为1
            page_size: 每页大小，默认为10000000
        返回: (success, task_data)
        """
        try:
            url = f"{self.base_url}/interface/task/task_list/"

            # 构造请求数据
            request_data = {
                "page": page,
                "page_size": page_size
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/json;charset=UTF-8'
            }

            response = self.session.post(url, json=request_data, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 修正数据结构：实际返回的是 task_list 而不是 results
                    results = data.get('data', {}).get('task_list', [])
                    paginator_data = data.get('data', {}).get('paginator_data', {})
                    total_count = paginator_data.get('total_records', 0)
                    logger.info(f"成功获取任务列表，当前页 {len(results)} 个任务，总计 {total_count} 个任务")
                    return True, data
                else:
                    logger.error(f"获取任务列表失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求任务列表失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"获取任务列表异常: {str(e)}")
            return False, None

    def get_all_tasks(self):
        """
        获取所有扫描任务（自动分页）
        返回: (success, all_tasks_list)
        """
        all_tasks = []
        page = 1
        page_size = 10000000  # 使用大页面大小

        logger.info("正在获取所有扫描任务...")

        try:
            success, task_data = self.get_task_list(page=page, page_size=page_size)
            if not success:
                return False, []

            tasks = task_data.get('data', {}).get('task_list', [])
            all_tasks.extend(tasks)

            logger.info(f"成功获取所有扫描任务，共 {len(all_tasks)} 个")
            return True, all_tasks

        except Exception as e:
            logger.error(f"获取所有任务异常: {str(e)}")
            return False, []

    def get_child_task_list(self, parent_id, page=1, page_size=1000000):
        """
        获取子任务列表
        参数:
            parent_id: 父任务ID
            page: 页码，默认为1
            page_size: 每页大小，默认为1000000
        返回: (success, child_task_data)
        """
        try:
            url = f"{self.base_url}/interface/task/child_task_list/"

            # 构造请求数据，参考CSV中的请求格式
            request_data = {
                "parent_id": parent_id,
                "page_size": page_size,
                "page": page
            }

            # 设置请求头
            headers = {
                'Content-Type': 'application/json;charset=UTF-8'
            }

            response = self.session.post(url, json=request_data, headers=headers)

            # 添加调试信息
            logger.info(f"请求URL: {url}")
            logger.info(f"请求数据: {request_data}")
            logger.info(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                logger.info(f"响应数据: {data}")

                if data.get('code') == 200:
                    child_tasks = data.get('data', {}).get('task_list', [])
                    paginator_data = data.get('data', {}).get('paginator_data', {})
                    total_child_tasks = paginator_data.get('total_child_tasks', 0)
                    logger.info(f"成功获取父任务 {parent_id} 的子任务列表，共 {len(child_tasks)} 个子任务，总计 {total_child_tasks} 个")
                    return True, data
                else:
                    logger.error(f"获取父任务 {parent_id} 的子任务列表失败: code={data.get('code')}, message={data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求父任务 {parent_id} 的子任务列表失败，状态码: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return False, None

        except Exception as e:
            logger.error(f"获取父任务 {parent_id} 的子任务列表异常: {str(e)}")
            return False, None

    def get_task_vulns(self, task_id, page=1, size=0, filter_levels='high,middle,low'):
        """
        获取指定任务的漏洞分布信息
        参数:
            task_id: 任务ID
            page: 页码，默认为1
            size: 每页大小，默认为0（获取所有）
            filter_levels: 过滤漏洞等级，默认为'high,middle,low'
        返回: (success, vuln_data)
        """
        try:
            # 构造URL，包含查询参数，参考CSV中的请求格式
            url = f"{self.base_url}/interface/report/sys/vuln-distribution/{task_id}"
            params = {
                'task_ids': task_id,
                'source': 'online',
                'page': page,
                'size': size,
                'filterVulnLevels': filter_levels,
                'vul_category_id': ''
            }

            response = self.session.get(url, params=params)

            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    vuln_info = data.get('data', {}).get('vulns_info', {})
                    vuln_list = vuln_info.get('vuln_distribution', {}).get('vuln_list', [])

                    # 获取统计信息
                    vuln_level_count = vuln_info.get('vuln_distribution', {}).get('vuln_level_count', {})
                    hosts_count = vuln_info.get('vuln_distribution', {}).get('hosts_count', 0)

                    logger.info(f"成功获取任务 {task_id} 的漏洞信息，共 {len(vuln_list)} 个漏洞，影响 {hosts_count} 个主机")
                    logger.info(f"漏洞等级分布: {vuln_level_count}")

                    return True, data
                else:
                    logger.error(f"获取任务 {task_id} 漏洞信息失败: {data.get('message', '未知错误')}")
                    return False, data
            else:
                logger.error(f"请求任务 {task_id} 漏洞信息失败，状态码: {response.status_code}")
                return False, None

        except Exception as e:
            logger.error(f"获取任务 {task_id} 漏洞信息异常: {str(e)}")
            return False, None


# 同步函数定义
def get_all_tasks_sync():
    """获取所有扫描任务"""
    logger.info("[绿盟漏扫] 开始获取所有扫描任务")

    try:
        # 使用默认参数
        host = "************"

        # 步骤1: 登录系统
        logger.info("步骤1: 登录绿盟系统")
        login_manager = GreenLeagueLogin(host=host)
        success, login_result = login_manager.auto_login()

        if not success:
            logger.error("登录失败，无法继续后续操作")
            return {
                "status": 1,
                "result": f"登录失败: {login_result}"
            }

        # 步骤2: 初始化任务管理器
        logger.info("步骤2: 初始化任务管理器")
        session = login_manager.get_session()
        base_url = login_manager.get_base_url()
        task_manager = TaskManager(session, base_url)

        # 步骤3: 获取所有任务
        logger.info("步骤3: 获取所有扫描任务")
        success, all_tasks = task_manager.get_all_tasks()

        if not success:
            return {
                "status": 1,
                "result": "获取所有任务失败"
            }

        logger.info(f"成功获取 {len(all_tasks)} 个扫描任务")

        return {
            "status": 0,
            "result": {
                'total_tasks': len(all_tasks),
                'tasks': all_tasks,
                'host': host
            }
        }

    except Exception as e:
        logger.error(f"获取所有任务出错: {str(e)}")
        return {
            "status": 1,
            "result": f"获取所有任务出错: {str(e)}"
        }


def get_child_tasks_sync(parent_id):
    """获取子任务"""
    logger.info(f"[绿盟漏扫] 开始获取父任务 {parent_id} 的子任务")

    try:
        # 使用默认参数
        host = "************"

        # 步骤1: 登录系统
        logger.info("步骤1: 登录绿盟系统")
        login_manager = GreenLeagueLogin(host=host)
        success, login_result = login_manager.auto_login()

        if not success:
            logger.error("登录失败，无法继续后续操作")
            return {
                "status": 1,
                "result": f"登录失败: {login_result}"
            }

        # 步骤2: 初始化任务管理器
        logger.info("步骤2: 初始化任务管理器")
        session = login_manager.get_session()
        base_url = login_manager.get_base_url()
        task_manager = TaskManager(session, base_url)

        # 步骤3: 获取子任务
        logger.info(f"步骤3: 获取父任务 {parent_id} 的子任务")
        success, child_task_data = task_manager.get_child_task_list(parent_id)

        if not success:
            return {
                "status": 1,
                "result": f"获取父任务 {parent_id} 的子任务失败"
            }

        child_tasks = child_task_data.get('data', {}).get('task_list', [])
        paginator_data = child_task_data.get('data', {}).get('paginator_data', {})

        logger.info(f"成功获取父任务 {parent_id} 的 {len(child_tasks)} 个子任务")

        return {
            "status": 0,
            "result": {
                'task_id': parent_id,
                'total_subtasks': len(child_tasks),
                'subtasks': child_tasks,
                'paginator_data': paginator_data,
                'host': host
            }
        }

    except Exception as e:
        logger.error(f"获取子任务出错: {str(e)}")
        return {
            "status": 1,
            "result": f"获取子任务出错: {str(e)}"
        }


def get_task_vulns_sync(task_id):
    """指定任务id获取所有漏洞信息"""
    logger.info(f"[绿盟漏扫] 开始获取任务 {task_id} 的所有漏洞信息")

    try:
        # 使用默认参数
        host = "************"

        # 步骤1: 登录系统
        logger.info("步骤1: 登录绿盟系统")
        login_manager = GreenLeagueLogin(host=host)
        success, login_result = login_manager.auto_login()

        if not success:
            logger.error("登录失败，无法继续后续操作")
            return {
                "status": 1,
                "result": f"登录失败: {login_result}"
            }

        # 步骤2: 初始化任务管理器
        logger.info("步骤2: 初始化任务管理器")
        session = login_manager.get_session()
        base_url = login_manager.get_base_url()
        task_manager = TaskManager(session, base_url)

        # 步骤3: 获取任务漏洞信息
        logger.info(f"步骤3: 获取任务 {task_id} 的漏洞信息")
        success, vuln_data = task_manager.get_task_vulns(task_id)

        if not success:
            return {
                "status": 1,
                "result": f"获取任务 {task_id} 的漏洞信息失败"
            }

        # 解析漏洞数据
        vuln_info = vuln_data.get('data', {}).get('vulns_info', {})
        vuln_distribution = vuln_info.get('vuln_distribution', {})
        vuln_list = vuln_distribution.get('vuln_list', [])
        vuln_level_count = vuln_distribution.get('vuln_level_count', {})
        hosts_count = vuln_distribution.get('hosts_count', 0)

        logger.info(f"成功获取任务 {task_id} 的漏洞信息，共 {len(vuln_list)} 个漏洞")

        return {
            "status": 0,
            "result": {
                'task_id': task_id,
                'total_vulns': len(vuln_list),
                'vulns': vuln_list,
                'vuln_level_count': vuln_level_count,
                'hosts_count': hosts_count,
                'host': host
            }
        }

    except Exception as e:
        logger.error(f"获取任务漏洞出错: {str(e)}")
        return {
            "status": 1,
            "result": f"获取任务漏洞出错: {str(e)}"
        }


# 创建Flask应用
app = Flask(__name__)

# 配置日志
logger.add("flask_app.log", rotation="10 MB", level="INFO")

def run_sync_function(func, *args, **kwargs):
    """
    运行同步函数的辅助函数
    """
    try:
        result = func(*args, **kwargs)
        return result
    except Exception as e:
        logger.error(f"运行同步函数失败: {str(e)}")
        return {
            "status": 1,
            "result": f"运行同步函数失败: {str(e)}"
        }

@app.route('/', methods=['GET'])
def index():
    """
    API首页，显示可用的接口
    """
    return jsonify({
        "message": "绿盟漏洞扫描器 Flask API 服务",
        "version": "1.0",
        "endpoints": {
            "获取所有扫描任务": {
                "url": "/api/tasks",
                "method": "GET",
                "description": "获取绿盟系统中的所有扫描任务列表"
            },
            "获取指定任务的子任务": {
                "url": "/api/tasks/<task_id>/subtasks",
                "method": "GET",
                "description": "获取指定任务的所有子任务信息",
                "parameters": {
                    "task_id": "任务ID (路径参数)"
                }
            },
            "获取指定任务的所有漏洞信息": {
                "url": "/api/tasks/<task_id>/vulnerabilities",
                "method": "GET",
                "description": "获取指定任务的所有漏洞详细信息",
                "parameters": {
                    "task_id": "任务ID (路径参数)"
                }
            }
        }
    })

@app.route('/api/tasks', methods=['GET'])
def get_all_scan_tasks():
    """
    获取所有扫描任务
    
    Returns:
        JSON响应包含所有扫描任务的列表
    """
    logger.info("API请求: 获取所有扫描任务")
    
    try:
        # 调用同步函数
        result = run_sync_function(get_all_tasks_sync)

        if result.get('status') == 0:
            logger.info(f"成功获取所有扫描任务，共 {result.get('result', {}).get('total_tasks', 0)} 个")
        else:
            logger.error(f"获取所有扫描任务失败: {result.get('result', '未知错误')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"API异常 - 获取所有扫描任务: {str(e)}")
        return jsonify({
            "status": 1,
            "result": f"API异常: {str(e)}"
        }), 500

@app.route('/api/tasks/<task_id>/subtasks', methods=['GET'])
def get_subtasks_by_task_id(task_id):
    """
    获取指定任务的子任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        JSON响应包含指定任务的所有子任务信息
    """
    logger.info(f"API请求: 获取任务 {task_id} 的子任务")
    
    try:
        # 验证task_id参数
        if not task_id:
            return jsonify({
                "status": 1,
                "result": "任务ID不能为空"
            }), 400
        
        # 调用同步函数
        result = run_sync_function(get_child_tasks_sync, int(task_id))

        if result.get('status') == 0:
            logger.info(f"成功获取任务 {task_id} 的子任务，共 {result.get('result', {}).get('total_subtasks', 0)} 个")
        else:
            logger.error(f"获取任务 {task_id} 的子任务失败: {result.get('result', '未知错误')}")

        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API异常 - 获取任务 {task_id} 的子任务: {str(e)}")
        return jsonify({
            "status": 1,
            "result": f"API异常: {str(e)}"
        }), 500

@app.route('/api/tasks/<task_id>/vulnerabilities', methods=['GET'])
def get_vulns_by_task_id(task_id):
    """
    获取指定任务的所有漏洞信息
    
    Args:
        task_id: 任务ID
        
    Returns:
        JSON响应包含指定任务的所有漏洞详细信息
    """
    logger.info(f"API请求: 获取任务 {task_id} 的所有漏洞信息")
    
    try:
        # 验证task_id参数
        if not task_id:
            return jsonify({
                "status": 1,
                "result": "任务ID不能为空"
            }), 400
        
        # 调用同步函数
        result = run_sync_function(get_task_vulns_sync, int(task_id))

        if result.get('status') == 0:
            vuln_count = result.get('result', {}).get('total_vulns', 0)
            logger.info(f"成功获取任务 {task_id} 的漏洞信息，共 {vuln_count} 个漏洞")
        else:
            logger.error(f"获取任务 {task_id} 的漏洞信息失败: {result.get('result', '未知错误')}")

        return jsonify(result)
        
    except Exception as e:
        logger.error(f"API异常 - 获取任务 {task_id} 的漏洞信息: {str(e)}")
        return jsonify({
            "status": 1,
            "result": f"API异常: {str(e)}"
        }), 500

@app.errorhandler(404)
def not_found(error):
    """
    404错误处理
    """
    _ = error  # 忽略未使用的参数
    return jsonify({
        "status": 1,
        "result": "API接口不存在"
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """
    500错误处理
    """
    _ = error  # 忽略未使用的参数
    return jsonify({
        "status": 1,
        "result": "服务器内部错误"
    }), 500

if __name__ == '__main__':
    """
    启动Flask应用
    """
    logger.info("启动绿盟漏洞扫描器 Flask API 服务")
    
    # 开发环境配置
    app.run(
        host='0.0.0.0',  # 允许外部访问
        port=35000,       # 端口号
        debug=True,      # 开启调试模式
        threaded=True    # 启用多线程
    )
